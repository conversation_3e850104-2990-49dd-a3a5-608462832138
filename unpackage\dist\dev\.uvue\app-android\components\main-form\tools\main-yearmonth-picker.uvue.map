{"version": 3, "sources": ["components/main-form/tools/main-yearmonth-picker.uvue"], "names": [], "mappings": "AA4DC,eAAc;AACd,KAAK,aAAY,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,eAAA,EAAA,uDAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;IACpB,IAAG,EAAI,MAAM,CAAA;IACb,KAAI,EAAI,MAAK,CAAA;CACd,CAAA;AAEA,KAAK,SAAQ,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,WAAA,EAAA,uDAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;IAChB,MAAK,EAAI,MAAM,CAAA;IACf,KAAI,EAAI,MAAK,CAAA;CACd,CAAA;AAEA,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,uBAAuB;IAC7B,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;IAC5B,KAAK,EAAE;QACN,OAAM;QACN,WAAW,EAAE;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,WAAG,EAAC,CAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAC;SACtC;QACD,OAAM;QACN,YAAY,EAAE;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,WAAG,EAAC,CAAE,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAC,GAAI,CAAA;SACvC;QACD,MAAM,EAAE;YACP,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,WAAG,EAAC,CAAE,EAAC;SAChB;QACD,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,WAAG,EAAC,CAAE,EAAC;SAEjB;KACA;IACD,IAAI;QACH,OAAO;YACN,SAAQ;YACR,OAAO,EAAE,KAAI,IAAK,OAAO;YACzB,UAAS;YACT,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAC,IAAK,MAAM;YAChD,UAAS;YACT,aAAa,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAC,GAAI,CAAC,CAAA,IAAK,MAAM;YACpD,OAAM;YACN,QAAQ,EAAE,EAAC,IAAK,MAAM,EAAE;YACxB,OAAM;YACN,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAA,IAAK,MAAM,EAAE;YAC9D,SAAQ;YACR,aAAa,EAAE,CAAA,IAAK,MAAK;SAC1B,CAAA;IACD,CAAC;IACD,OAAO;QACN,IAAI,CAAC,cAAc,EAAC,CAAA;IACrB,CAAC;IACD,OAAO,EAAE;QACR,QAAO;QACP,cAAc;YACb,IAAI,CAAC,YAAW,GAAI,IAAI,CAAC,WAAU,CAAA;YACnC,IAAI,CAAC,aAAY,GAAI,IAAI,CAAC,YAAW,CAAA;YACrC,IAAI,CAAC,gBAAgB,EAAC,CAAA;QACvB,CAAC;QAED,SAAQ;QACR,gBAAgB;YACf,MAAM,WAAU,EAAI,MAAK,GAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAC,CAAA;YAGpD,MAAM,SAAQ,EAAI,MAAK,GAAI,WAAU,GAAI,IAAI,CAAC,MAAK,CAAA;YACnD,MAAM,OAAM,EAAI,MAAK,GAAI,WAAU,GAAI,IAAI,CAAC,KAAI,CAAA;YAEhD,IAAI,CAAC,QAAO,GAAI,EAAC,CAAA;YACjB,KAAK,IAAI,IAAG,GAAI,SAAS,EAAE,IAAG,IAAK,OAAO,EAAE,IAAI,EAAE,EAAE;gBACnD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAA,CAAA;aACxB;QACD,CAAC;QAED,SAAQ;QACR,YAAY,CAAC,IAAG,EAAI,MAAM;YACzB,IAAI,CAAC,YAAW,GAAI,IAAG,CAAA;QACxB,CAAC;QAED,SAAQ;QACR,aAAa,CAAC,KAAI,EAAI,MAAM;YAC3B,IAAI,CAAC,aAAY,GAAI,KAAI,CAAA;QAC1B,CAAC;QAED,OAAM;QACN,IAAI;YACH,IAAI,CAAC,OAAM,GAAI,IAAG,CAAA;YAClB,IAAI,CAAC,2BAA2B,EAAC,CAAA;QAClC,CAAC;QAED,OAAM;QACN,KAAK;YACJ,IAAI,CAAC,OAAM,GAAI,KAAI,CAAA;QACpB,CAAC;QAED,WAAU;QACV,2BAA2B;YAC1B,2BAA0B;YAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,EAAC;gBAClB,MAAM,aAAY,EAAI,MAAK,GAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAA,CAAA;gBACtE,IAAI,aAAY,IAAK,CAAC,CAAC,EAAE;oBACxB,8BAA6B;oBAC7B,IAAI,CAAC,aAAY,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,aAAY,GAAI,CAAC,CAAA,GAAI,EAAE,CAAA,CAAA;iBAC1D;YACD,CAAC,CAAA,CAAA;QACF,CAAC;QAED,YAAW;QACX,cAAc;YACb,IAAI,CAAC,KAAK,EAAC,CAAA;YACX,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAA,CAAA;QACpB,CAAC;QAED,WAAU;QACV,QAAQ;YACP,IAAI,CAAC,KAAK,EAAC,CAAA;YACX,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAA,CAAA;QACpB,CAAC;QAED,WAAU;QACV,SAAS;YAER,MAAM,MAAK,EAAI,aAAa,GAAE,EAAA,mBAAA,EAAA,IAAA,oBAAA,CAAA,QAAA,EAAA,uDAAA,EAAA,GAAA,EAAA,EAAA,CAAA;gBAC7B,IAAI,EAAE,IAAI,CAAC,YAAY;gBACvB,KAAK,EAAE,IAAI,CAAC,aAAY;aACzB,CAAA;YACA,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAA,CAAA;YAC5B,IAAI,CAAC,KAAK,EAAC,CAAA;QACZ,CAAA;KACD;CACD,CAAA,CAAA;;;;;kBA9LY,IAAA,CAAA,OAAO,CAAA;UAAnB,GAAA,CAsDO,MAAA,EAAA,GAAA,CAAA;;YAtDc,KAAK,EAAC,gBAAgB;YAAE,OAAK,EAAE,IAAA,CAAA,cAAc;;YACjE,GAAA,CAoDO,MAAA,EAAA,GAAA,CAAA;gBApDD,KAAK,EAAC,cAAc;gBAAE,OAAK,EAAA,aAAA,CAAN,GAAA,EAAA,GAAA,CAAc,EAAA,CAAA,MAAA,CAAA,CAAA;;gBACxC,GAAA,CAkDO,MAAA,EAAA,GAAA,CAAA,EAlDD,KAAK,EAAC,4BAA4B,EAAA,CAAA,EAAA;oBAEvC,GAAA,CAMO,MAAA,EAAA,GAAA,CAAA,EAND,KAAK,EAAC,QAAQ,EAAA,CAAA,EAAA;wBACnB,GAAA,CAA4D,MAAA,EAAA,GAAA,CAAA;4BAAtD,KAAK,EAAC,oBAAoB;4BAAE,OAAK,EAAE,IAAA,CAAA,QAAQ;4BAAE,IAAE,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;wBACrD,GAAA,CAAmC,MAAA,EAAA,GAAA,CAAA,EAA7B,KAAK,EAAC,WAAW,EAAA,CAAA,EAAC,MAAI,CAAA;wBAC5B,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA,EAFD,KAAK,EAAC,uBAAuB,EAAA,CAAA,EAAA;4BAClC,GAAA,CAA8D,MAAA,EAAA,GAAA,CAAA;gCAAxD,KAAK,EAAC,qBAAqB;gCAAE,OAAK,EAAE,IAAA,CAAA,SAAS;gCAAE,IAAE,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;;;oBAKzD,GAAA,CAgCO,MAAA,EAAA,GAAA,CAAA,EAhCD,KAAK,EAAC,aAAa,EAAA,CAAA,EAAA;wBAExB,GAAA,CAaO,MAAA,EAAA,GAAA,CAAA,EAbD,KAAK,EAAC,gBAAgB,EAAA,CAAA,EAAA;4BAC3B,GAAA,CAAqC,MAAA,EAAA,GAAA,CAAA,EAA/B,KAAK,EAAC,eAAe,EAAA,CAAA,EAAC,IAAE,CAAA;4BAE9B,GAAA,CASc,aAAA,EAAA,GAAA,CAAA;gCATD,SAAS,EAAC,UAAU;gCAAC,KAAK,EAAC,aAAa;gCAAE,YAAU,EAAE,IAAA,CAAA,aAAa;;gCAE/E,GAAA,CAKO,MAAA,EAAA,GAAA,CAAA,EALD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;oCACtB,GAAA,CAGO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CAHc,IAAA,CAAA,QAAQ,EAAA,CAAhB,IAAI,EAAJ,KAAI,EAAJ,OAAI,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;+CAAjB,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA;4CAHyB,GAAG,EAAE,IAAI;4CAAE,KAAK,EAAA,GAAA,CAAA,CAAC,WAAW,EACnD,GAAA,CAAA,EAAA,aAAA,EAAA,IAAA,IAAA,IAAA,CAAA,YAAA,EAAA,CAAuC,CAAA,CAAA;4CAAG,OAAK,EAAA,GAAA,EAAA,GAAE,IAAA,CAAA,YAAY,CAAC,IAAI,CAAA,CAAA,CAAA,CAAA;;4CAC1E,GAAA,CAA+F,MAAA,EAAA,GAAA,CAAA;gDAAzF,KAAK,EAAA,GAAA,CAAA,CAAC,WAAW,EAAS,GAAA,CAAA,EAAA,kBAAA,EAAA,IAAA,IAAA,IAAA,CAAA,YAAA,EAAA,CAA4C,CAAA,CAAA;oDAAK,IAAI,CAAA,EAAA,CAAA,CAAA,iBAAA,CAAA;;;;;;wBAQzF,GAAA,CAaO,MAAA,EAAA,GAAA,CAAA,EAbD,KAAK,EAAC,oCAAoC,EAAA,CAAA,EAAA;4BAC/C,GAAA,CAAqC,MAAA,EAAA,GAAA,CAAA,EAA/B,KAAK,EAAC,eAAe,EAAA,CAAA,EAAC,IAAE,CAAA;4BAE9B,GAAA,CASc,aAAA,EAAA,GAAA,CAAA;gCATD,SAAS,EAAC,UAAU;gCAAC,KAAK,EAAC,cAAc;;gCAErD,GAAA,CAKO,MAAA,EAAA,GAAA,CAAA,EALD,KAAK,EAAC,YAAY,EAAA,CAAA,EAAA;oCACvB,GAAA,CAGO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CAHe,IAAA,CAAA,SAAS,EAAA,CAAlB,KAAK,EAAL,KAAK,EAAL,OAAK,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;+CAAlB,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA;4CAH2B,GAAG,EAAE,KAAK;4CAAE,KAAK,EAAA,GAAA,CAAA,CAAC,YAAY,EACvD,GAAA,CAAA,EAAA,cAAA,EAAA,KAAA,IAAA,IAAA,CAAA,aAAA,EAAA,CAA0C,CAAA,CAAA;4CAAG,OAAK,EAAA,GAAA,EAAA,GAAE,IAAA,CAAA,aAAa,CAAC,KAAK,CAAA,CAAA,CAAA,CAAA;;4CAC/E,GAAA,CAAqG,MAAA,EAAA,GAAA,CAAA;gDAA/F,KAAK,EAAA,GAAA,CAAA,CAAC,YAAY,EAAS,GAAA,CAAA,EAAA,mBAAA,EAAA,KAAA,IAAA,IAAA,CAAA,aAAA,EAAA,CAA+C,CAAA,CAAA;oDAAK,KAAK,CAAA,GAAG,GAAC,EAAA,CAAA,CAAA,iBAAA,CAAA;;;;;;;oBASnG,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA,EAHD,KAAK,EAAC,mBAAmB,EAAA,CAAA,EAAA;wBAC9B,GAAA,CAA0C,MAAA,EAAA,GAAA,CAAA,EAApC,KAAK,EAAC,iBAAiB,EAAA,CAAA,EAAC,OAAK,CAAA;wBACnC,GAAA,CAA4E,MAAA,EAAA,GAAA,CAAA,EAAtE,KAAK,EAAC,iBAAiB,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,YAAY,CAAA,GAAG,GAAC,GAAA,GAAA,CAAG,IAAA,CAAA,aAAa,CAAA,GAAG,GAAC,EAAA,CAAA,CAAA,UAAA,CAAA", "file": "components/main-form/tools/main-yearmonth-picker.uvue", "sourcesContent": ["<template>\r\n\t<!-- 弹窗遮罩层 -->\r\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\r\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\r\n\t\t\t<view class=\"yearmonth-picker-container\">\r\n\t\t\t\t<!-- 导航栏 -->\r\n\t\t\t\t<view class=\"navbar\">\r\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\r\n\t\t\t\t\t<text class=\"nav-title\">选择年月</text>\r\n\t\t\t\t\t<view class=\"confirm-btn-container\">\r\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 年月选择区域 -->\r\n\t\t\t\t<view class=\"picker-body\">\r\n\t\t\t\t\t<!-- 年份选择区域 -->\r\n\t\t\t\t\t<view class=\"picker-section\">\r\n\t\t\t\t\t\t<text class=\"section-title\">年份</text>\r\n\r\n\t\t\t\t\t\t<scroll-view direction=\"vertical\" class=\"year-scroll\" :scroll-top=\"yearScrollTop\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"year-list\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"year in yearList\" :key=\"year\" class=\"year-item\"\r\n\t\t\t\t\t\t\t\t\t:class=\"{ 'year-active': year == selectedYear }\" @click=\"onYearSelect(year)\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"year-text\" :class=\"{ 'year-text-active': year == selectedYear }\">{{ year }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 月份选择区域 -->\r\n\t\t\t\t\t<view class=\"picker-section picker-section-last\">\r\n\t\t\t\t\t\t<text class=\"section-title\">月份</text>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t<scroll-view direction=\"vertical\" class=\"month-scroll\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"month-grid\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"month in monthList\" :key=\"month\" class=\"month-item\"\r\n\t\t\t\t\t\t\t\t\t:class=\"{ 'month-active': month == selectedMonth }\" @click=\"onMonthSelect(month)\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"month-text\" :class=\"{ 'month-text-active': month == selectedMonth }\">{{ month }}月</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 当前选择显示区域 -->\r\n\t\t\t\t<view class=\"current-selection\">\r\n\t\t\t\t\t<text class=\"selection-label\">当前选择：</text>\r\n\t\t\t\t\t<text class=\"selection-value\">{{ selectedYear }}年{{ selectedMonth }}月</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 定义年月选择器的数据类型\r\n\ttype YearMonthData = {\r\n\t\tyear : number,\r\n\t\tmonth : number\r\n\t}\r\n\r\n\ttype YearRange = {\r\n\t\tbefore : number,\r\n\t\tafter : number\r\n\t}\r\n\r\n\texport default {\r\n\t\tname: \"main-yearmonth-picker\",\r\n\t\temits: ['cancel', 'confirm'],\r\n\t\tprops: {\r\n\t\t\t// 初始年份\r\n\t\t\tinitialYear: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => new Date().getFullYear()\r\n\t\t\t},\r\n\t\t\t// 初始月份\r\n\t\t\tinitialMonth: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => new Date().getMonth() + 1\r\n\t\t\t},\r\n\t\t\tbefore: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => 50\r\n\t\t\t},\r\n\t\t\tafter: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => 10\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 控制弹窗显示\r\n\t\t\t\tvisible: false as boolean,\r\n\t\t\t\t// 当前选中的年份\r\n\t\t\t\tselectedYear: new Date().getFullYear() as number,\r\n\t\t\t\t// 当前选中的月份\r\n\t\t\t\tselectedMonth: (new Date().getMonth() + 1) as number,\r\n\t\t\t\t// 年份列表\r\n\t\t\t\tyearList: [] as number[],\r\n\t\t\t\t// 月份列表\r\n\t\t\t\tmonthList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] as number[],\r\n\t\t\t\t// 年份滚动位置\r\n\t\t\t\tyearScrollTop: 0 as number\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.initializeData()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化数据\r\n\t\t\tinitializeData() {\r\n\t\t\t\tthis.selectedYear = this.initialYear\r\n\t\t\t\tthis.selectedMonth = this.initialMonth\r\n\t\t\t\tthis.generateYearList()\r\n\t\t\t},\r\n\r\n\t\t\t// 生成年份列表\r\n\t\t\tgenerateYearList() {\r\n\t\t\t\tconst currentYear : number = new Date().getFullYear()\r\n\r\n\r\n\t\t\t\tconst startYear : number = currentYear - this.before\r\n\t\t\t\tconst endYear : number = currentYear + this.after\r\n\r\n\t\t\t\tthis.yearList = []\r\n\t\t\t\tfor (let year = startYear; year <= endYear; year++) {\r\n\t\t\t\t\tthis.yearList.push(year)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 年份选择事件\r\n\t\t\tonYearSelect(year : number) {\r\n\t\t\t\tthis.selectedYear = year\r\n\t\t\t},\r\n\r\n\t\t\t// 月份选择事件\r\n\t\t\tonMonthSelect(month : number) {\r\n\t\t\t\tthis.selectedMonth = month\r\n\t\t\t},\r\n\r\n\t\t\t// 打开弹窗\r\n\t\t\topen() {\r\n\t\t\t\tthis.visible = true\r\n\t\t\t\tthis.calculateYearScrollPosition()\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭弹窗\r\n\t\t\tclose() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t},\r\n\r\n\t\t\t// 计算年份滚动位置\r\n\t\t\tcalculateYearScrollPosition() {\r\n\t\t\t\t// 在下一个tick中计算滚动位置，确保DOM已更新\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tconst selectedIndex : number = this.yearList.indexOf(this.selectedYear)\r\n\t\t\t\t\tif (selectedIndex != -1) {\r\n\t\t\t\t\t\t// 每个年份项高度约为60rpx，让选中年份显示在中间位置\r\n\t\t\t\t\t\tthis.yearScrollTop = Math.max(0, (selectedIndex - 2) * 30)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 点击遮罩层关闭弹窗\r\n\t\t\tonOverlayClick() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 取消按钮点击事件\r\n\t\t\tonCancel() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 确定按钮点击事件\r\n\t\t\tonConfirm() {\r\n\t\t\t\t\r\n\t\t\t\tconst result : UTSJSONObject= {\r\n\t\t\t\t\tyear: this.selectedYear,\r\n\t\t\t\t\tmonth: this.selectedMonth\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('confirm', result)\r\n\t\t\t\tthis.close()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 弹窗遮罩层 */\r\n\t.picker-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.picker-modal {\r\n\t\twidth: 90%;\r\n\t\tmax-width: 600rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.yearmonth-picker-container {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 导航栏样式 */\r\n\t.navbar {\r\n\t\theight: 44px;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-bottom: 1px solid #e5e5e5;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.nav-btn {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #007aff;\r\n\t\tpadding: 8px 12px;\r\n\t}\r\n\r\n\t.cancel-btn {\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.confirm-btn-container {\r\n\t\theight: 30px;\r\n\t\tbackground-color: #007aff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.nav-title {\r\n\t\tfont-size: 17px;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t/* 选择器主体区域 */\r\n\t.picker-body {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\theight: 400rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.picker-section {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tborder-right: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.picker-section-last {\r\n\t\tflex:2;\r\n\t\tborder-right: none;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\ttext-align: center;\r\n\t\tpadding: 20rpx 0;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t/* 年份选择区域 */\r\n\t.year-scroll {\r\n\t\tflex: 1;\r\n\t\theight: 340rpx;\r\n\t}\r\n\r\n\t.year-list {\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n\r\n\t.year-item {\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tmargin: 0 15rpx 8rpx 15rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.year-item.year-active {\r\n\t\tbackground-color: #007aff;\r\n\t\ttransform: scale(1.05);\r\n\t}\r\n\r\n\t.year-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.year-text-active {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* 月份选择区域 */\r\n\t.month-scroll {\r\n\t\tflex: 1;\r\n\t\theight: 340rpx;\r\n\t}\r\n\r\n\t.month-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 20rpx 15rpx;\r\n\t\tjustify-content: space-between;\r\n\t\talign-content: flex-start;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.month-item {\r\n\t\twidth: 100rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tborder-radius: 10rpx;\r\n\t\tmargin-bottom: 12rpx;\r\n\t}\r\n\r\n\t.month-item.month-active {\r\n\t\tbackground-color: #007aff;\r\n\t\ttransform: scale(1.05);\r\n\t}\r\n\r\n\t.month-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.month-text-active {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* 当前选择显示区域 */\r\n\t.current-selection {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.selection-label {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.selection-value {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #007aff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n</style>"]}