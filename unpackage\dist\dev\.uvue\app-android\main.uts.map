{"version": 3, "file": "main.uts", "sourceRoot": "", "sources": ["main.uts"], "names": [], "mappings": "AAAA,OAAO,uGAAuG,CAAC;AAAA,OAAO,GAAG,MAAM,YAAY,CAAA;AAE3I,OAAO,EAAE,YAAY,EAAE,MAAM,KAAK,CAAA;AAClC,MAAM,UAAU,SAAS;IACxB,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAA;IAC7B,OAAO;QACN,GAAG;KACH,CAAA;AACF,CAAC;AACD,MAAM,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI;IAC1B,gBAAgB,EAAE,CAAC;IACnB,eAAe,EAAE,CAAC;IAClB,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,EAAE,CAAC,CAAC;AAC3D,CAAC;AAED,MAAM,OAAO,YAAa,SAAQ,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS;IACjE,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,UAAU,CAAA;IAClC,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,gBAAgB,CAAA;IACzC,QAAQ,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAA;IACtC,QAAQ,CAAC,WAAW,EAAE,MAAM,GAAG,KAAK,CAAA;IACpC,QAAQ,CAAC,kBAAkB,EAAE,MAAM,GAAG,MAAM,CAAA;IAE5C,gBAAgB,KAAK,EAAE,CAAA,CAAC,CAAC;CAC5B;AAED,OAAO,uBAAuB,MAAM,0BAA0B,CAAA;AAC9D,OAAO,6BAA6B,MAAM,gCAAgC,CAAA;AAC1E,SAAS,gBAAgB;IACzB,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,SAAS,EAAE,uBAAuB,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,WAAW,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,wBAAwB,EAAC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,YAAY,CAAC,CAAA;IAChM,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,yBAAyB,EAAE,SAAS,EAAE,6BAA6B,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,WAAW,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,wBAAwB,EAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,YAAY,CAAC,CAAA;AACtM,CAAC;AACD,MAAM,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,CAAA;AACxD,MAAM,eAAe,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,EAAC,mBAAmB,CAAC,EAAC,CAAC,OAAO,EAAC,GAAG,CAAC,CAAC,CAAC,wBAAwB,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAC3I,SAAS,eAAe;IACtB,WAAW,CAAC,aAAa,GAAG,oBAAoB,CAAA;IAChD,WAAW,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,wBAAwB,EAAC,OAAO,CAAC,EAAC,CAAC,wBAAwB,EAAC,WAAW,CAAC,EAAC,CAAC,8BAA8B,EAAC,SAAS,CAAC,EAAC,CAAC,iBAAiB,EAAC,SAAS,CAAC,CAAC,CAAC,CAAA;IACnL,WAAW,CAAC,eAAe,GAAG,IAAG,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAE,IAAI,CAAA;IACjE,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,eAAe,EAAE,CAAA;IAClD,WAAW,CAAC,YAAY,GAAG,EAAE,CAAA;IAC7B,WAAW,CAAC,WAAW,GAAG,GAAG,EAAE,CAAA;IAE/B,WAAW,CAAC,KAAK,GAAG,IAAI,CAAA;AAC1B,CAAC", "sourcesContent": ["import 'D:/Soft/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts';import App from './App.uvue'\r\n\r\nimport { createSSRApp } from 'vue'\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\treturn {\r\n\t\tapp\r\n\t}\r\n}\nexport function main(app: IApp) {\n    definePageRoutes();\n    defineAppConfig();\n    (createApp()['app'] as VueApp).mount(app, GenUniApp());\n}\n\nexport class UniAppConfig extends io.dcloud.uniapp.appframe.AppConfig {\n    override name: string = \"QitTools\"\n    override appid: string = \"__UNI__C178CB1\"\n    override versionName: string = \"1.0.0\"\n    override versionCode: string = \"100\"\n    override uniCompilerVersion: string = \"4.75\"\n    \n    constructor() { super() }\n}\n\nimport GenPagesIndexIndexClass from './pages/index/index.uvue'\nimport GenPagesCalendarCalendarClass from './pages/calendar/calendar.uvue'\nfunction definePageRoutes() {\n__uniRoutes.push({ path: \"pages/index/index\", component: GenPagesIndexIndexClass, meta: { isQuit: true } as UniPageMeta, style: _uM([[\"navigationBarTitleText\",\"uni-app x\"]]) } as UniPageRoute)\n__uniRoutes.push({ path: \"pages/calendar/calendar\", component: GenPagesCalendarCalendarClass, meta: { isQuit: false } as UniPageMeta, style: _uM([[\"navigationBarTitleText\",\"日历\"]]) } as UniPageRoute)\n}\nconst __uniTabBar: Map<string, any | null> | null = null\nconst __uniLaunchPage: Map<string, any | null> = _uM([[\"url\",\"pages/index/index\"],[\"style\",_uM([[\"navigationBarTitleText\",\"uni-app x\"]])]])\nfunction defineAppConfig(){\n  __uniConfig.entryPagePath = '/pages/index/index'\n  __uniConfig.globalStyle = _uM([[\"navigationBarTextStyle\",\"black\"],[\"navigationBarTitleText\",\"uni-app x\"],[\"navigationBarBackgroundColor\",\"#F8F8F8\"],[\"backgroundColor\",\"#F8F8F8\"]])\n  __uniConfig.getTabBarConfig = ():Map<string, any> | null =>  null\n  __uniConfig.tabBar = __uniConfig.getTabBarConfig()\n  __uniConfig.conditionUrl = ''\n  __uniConfig.uniIdRouter = _uM()\n  \n  __uniConfig.ready = true\n}\n"]}