{"version": 3, "sources": ["pages/calendar/calendar.uvue"], "names": [], "mappings": "AAgBA,OAAO,EAAE,QAAQ,EAAE,QAAO,EAAE,MAAO,aAAY,CAAA;AAE/C,KAAK,UAAS,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,YAAA,EAAA,8BAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;IAChB,CAAC,EAAE,MAAM,CAAA;IACT,CAAC,EAAE,MAAM,CAAA;IACT,KAAK,EAAE,MAAM,CAAA;IACb,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,QAAO,CAAA;CACf,CAAA;AAEA,MAAK,OAAQ,GAAE,eAAA,CAAA;IACb,IAAG;QACD,OAAO;YACL,KAAK,EAAE,EAAC,IAAK,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACnC,OAAO,EAAE,EAAC,IAAK,KAAK,CAAC,UAAU,CAAC;YAChC,SAAS,EAAE,IAAI,QAAQ,EAAC,IAAK,QAAQ;YACrC,QAAQ,EAAE;gBACR,QAAQ,EAAE,EAAE;gBACZ,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE,CAAC;gBACP,GAAG,EAAE,CAAC;gBACN,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,KAAI;aAChB,IAAK,QAAQ;YACb,SAAS,EAAE,CAAA;SACb,CAAA;IACF,CAAC;IACD,QAAQ,EAAE;QACR,OAAM;QACN,aAAY,IAAM,MAAK;YACrB,MAAM,OAAM,GAAI,IAAI,CAAC,QAAO,CAAA;YAC5B,MAAM,KAAI,GAAI,OAAO,CAAC,KAAI,CAAA;YAC1B,OAAO,KAAI,GAAI,EAAC,CAAE,CAAA,CAAE,GAAE,GAAI,KAAI,CAAE,CAAA,CAAE,KAAK,CAAC,QAAQ,IAAC,CAAA;QACnD,CAAC;QACD,WAAU,IAAM,MAAK;YACnB,MAAM,IAAG,GAAI,IAAI,CAAC,QAAQ,CAAC,IAAG,CAAA;YAC9B,IAAI,IAAG,IAAK,IAAI,EAAE;gBAChB,OAAO,EAAC,CAAA;aACV;YACA,OAAO,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,MAAK,CAAA;QACnC,CAAA;KACD;IACD,OAAM,KAAO,CAAC;IACd,OAAM;QACJ,MAAM,QAAO,GAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAA,IAAK,QAAO,CAAA;QACnD,IAAI,CAAC,KAAI,GAAI,QAAQ,CAAC,QAAQ,EAAC,CAAA;QAC/B,IAAI,CAAC,QAAO,GAAI,QAAQ,CAAC,WAAW,EAAC,CAAA;QACrC,SAAQ;QACR,IAAI,CAAC,UAAU,EAAC,CAAA;QAChB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAA,CAAA;QAE5B,SAAQ;QACR,MAAM,MAAK,GAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAA,IAAK,UAAS,CAAA;QACrD,IAAI,CAAC,SAAQ,GAAI,MAAM,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAA;IACvD,CAAC;IACD,OAAO,EAAE;QAEP,uBAAsB;QACtB,MAAK,CAAG,KAAK,EAAE,UAAU;YACvB,MAAM,IAAG,GAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAA,IAAK,UAAS,CAAA;YAClD,MAAM,IAAG,GAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;YACzC,MAAM,KAAI,GAAI,IAAI,CAAC,IAAI,CAAA,CAAE,mBAAkB;YAC3C,MAAM,KAAI,GAAI,IAAI,CAAC,GAAG,CAAA,CAAE,mBAAkB;YAC1C,MAAM,KAAI,GAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;YAC9B,MAAM,OAAM,GAAI,KAAK,CAAC,OAAO,CAAA,CAAE,OAAM;YACrC,MAAM,OAAM,GAAI,KAAK,CAAC,OAAO,CAAA,CAAE,OAAM;YACrC,YAAW;YACX,MAAM,CAAA,GAAI,OAAM,GAAI,KAAI,CAAA;YACxB,MAAM,CAAA,GAAI,OAAM,GAAI,KAAI,CAAA;YAExB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAA,CAAA;QACrB,CAAC;QAED,YAAW;QACX,SAAQ,CAAG,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;YAC7B,QAAO;YACP,uCAAsC;YACtC,MAAM,QAAO,GAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAA,IAAK,QAAO,CAAA;YACnD,MAAM,SAAQ,GAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAA,IAAK,KAAK,CAAC,UAAU,CAAA,CAAA;YAE3D,wBAAuB;YACvB,KAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,MAAM,IAAG,GAAI,SAAS,CAAC,CAAC,CAAA,CAAA;gBACxB,eAAc;gBACd,MAAM,KAAI,GAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAC,KAAI,CAAA;gBAChC,MAAM,KAAI,GAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAC,MAAK,CAAA;gBAEjC,MAAM,UAAS,GAAI,IAAI,CAAC,CAAA,GAAI,CAAA,IAAK,CAAA,GAAI,KAAI,CAAA;gBACzC,MAAM,UAAS,GAAI,IAAI,CAAC,CAAA,GAAI,CAAA,IAAK,CAAA,GAAI,KAAI,CAAA;gBAEzC,MAAM,SAAQ,GAAI,UAAS,IAAK,UAAS,CAAA;gBAEzC,IAAI,SAAS,EAAE;oBACb,MAAM,IAAG,GAAI,IAAI,CAAC,IAAG,CAAA;oBACrB,IAAI,CAAC,QAAO,GAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAA,CAAA;oBAClD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA,CAAA;iBAC9C;aACF;QACF,CAAC;QACD,QAAO;QACP,OAAM;YACJ,MAAM,QAAO,GAAI,IAAI,CAAC,QAAQ,CAAC,QAAO,CAAA;YACtC,MAAM,QAAO,GAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAA,IAAK,QAAO,CAAA;YACnD,IAAI,IAAG,GAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO,CAAA,CAAA;YACjD,MAAM,OAAM,GAAI,IAAI,CAAC,IAAG,GAAI,GAAE,GAAI,IAAI,CAAC,KAAI,GAAI,IAAI,CAAA;YACnD,IAAG,GAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAA,CAAA;YAE/B,IAAI,CAAC,QAAO,GAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAA,CAAA;YAClD,IAAI,CAAC,KAAI,GAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAA,CAAA;YAE5C,aAAY;YACZ,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC9C,IAAI,CAAC,SAAS,EAAE,CAAA;aAClB;iBAAO;gBACL,WAAU;gBACV,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAA,CAAA;aACzC;QACF,CAAC;QACD,QAAO;QACP,QAAO;YACL,MAAM,QAAO,GAAI,IAAI,CAAC,QAAQ,CAAC,QAAO,CAAA;YACtC,MAAM,QAAO,GAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAA,IAAK,QAAO,CAAA;YACnD,IAAI,IAAG,GAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAA,CAAA;YAEhD,MAAM,OAAM,GAAI,IAAI,CAAC,IAAG,GAAI,GAAE,GAAI,IAAI,CAAC,KAAI,GAAI,IAAI,CAAA;YACnD,IAAG,GAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAA,CAAA;YAE/B,IAAI,CAAC,QAAO,GAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAA,CAAA;YAClD,IAAI,CAAC,KAAI,GAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAA,CAAA;YAE5C,aAAY;YACZ,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC9C,IAAI,CAAC,SAAS,EAAE,CAAA;aAClB;iBAAO;gBACL,WAAU;gBACV,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAA,CAAA;aACzC;QACF,CAAC;QACD,OAAM;QACN,SAAQ;YACN,MAAM,QAAO,GAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAA,IAAK,QAAO,CAAA;YACnD,MAAM,IAAG,GAAI,QAAQ,CAAC,OAAO,EAAC,CAAA;YAC9B,IAAI,CAAC,QAAO,GAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAA,CAAA;YAClD,IAAI,CAAC,KAAI,GAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAA,CAAA;YAE5C,SAAQ;YACR,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAA,CAAA;QACzC,CAAC;QACD,YAAW;QACX,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,OAAM;YACjD,MAAM,KAAI,GAAI,IAAI,IAAI,EAAE,CAAA;YACxB,OAAO,IAAG,KAAM,KAAK,CAAC,WAAW,EAAC,IAAK,KAAI,KAAM,KAAK,CAAC,QAAQ,EAAC,GAAI,CAAC,CAAA;QACvE,CAAC;QAED,WAAU;QACV,UAAS;YACP,MAAM,IAAG,GAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAA,IAAK,UAAS,CAAA;YACnD,IAAI,GAAE,GAAI,IAAI,CAAC,kBAAkB,EAAC,CAAA;YAClC,IAAI,GAAE,IAAK,IAAI;gBAAE,OAAK;YACtB,MAAM,eAAc,GAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAA,CAAA;YAE1D,MAAM,KAAI,GAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC,KAAI,CAAA;YAC/C,MAAM,GAAE,GAAI,eAAe,CAAC,MAAK,CAAA;YACjC,MAAM,SAAQ,GAAI,KAAI,GAAI,GAAE,CAAA;YAE5B,GAAG,CAAC,IAAG,GAAI,IAAG,CAAA;YACd,GAAG,CAAC,SAAQ,GAAI,QAAO,CAAA;YAEvB,KAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC5B,IAAI,QAAO,GAAI,CAAA,GAAI,SAAQ,GAAI,CAAA,CAAA;gBAC/B,IAAI,SAAQ,GAAI,SAAQ,GAAI,CAAA,CAAA;gBAC5B,IAAI,UAAS,GAAI,EAAC,CAAA;gBAElB,OAAM;gBACN,MAAM,IAAG,GAAI,eAAe,CAAC,CAAC,CAAA,CAAA;gBAC9B,IAAI,SAAQ,GAAI,SAAQ,GAAI,CAAA,GAAI,QAAO,CAAA;gBACvC,IAAI,QAAO,GAAI,UAAS,GAAI,CAAA,GAAI,CAAA,CAAA;gBAEhC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAA,CAAA;aACxC;YACA,GAAG,CAAC,MAAM,EAAC,CAAA;QACb,CAAC;QAED,SAAQ;QACR,QAAO,CAAG,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM;YACnD,MAAM,UAAS,GAAI,IAAI,CAAC,GAAG,EAAC,CAAA;YAC5B,MAAM,IAAG,GAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAA,IAAK,UAAS,CAAA;YAClD,IAAI,GAAE,GAAI,IAAI,CAAC,kBAAkB,EAAC,CAAA;YAClC,IAAI,GAAE,IAAK,IAAI;gBAAE,OAAK;YACtB,MAAM,GAAE,GAAI,IAAI,CAAC,qBAAqB,EAAC,CAAA;YACvC,MAAM,KAAI,GAAI,GAAG,CAAC,KAAI,CAAA;YACtB,MAAM,MAAK,GAAI,GAAG,CAAC,MAAK,CAAA;YACxB,IAAI,QAAO,GAAI,KAAK,CAAC,MAAK,CAAA;YAC1B,MAAM,SAAQ,GAAI,KAAI,GAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAK,CAAA;YACxC,MAAM,UAAS,GAAI,MAAK,GAAI,QAAO,CAAA;YAEnC,IAAI,IAAG,KAAM,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,SAAS,CAAA,GAAI,EAAC,IAAK,KAAK,CAAC,UAAU,CAAA,CAAA;gBAC9C,GAAG,CAAC,KAAK,EAAC,CAAA;aACZ;YAEA,GAAG,CAAC,SAAQ,GAAI,QAAO,CAAA;YAEvB,KAAK,IAAI,IAAG,GAAI,CAAC,EAAE,IAAG,GAAI,QAAQ,EAAE,IAAI,EAAE,EAAE;gBAE1C,MAAM,SAAQ,GAAI,KAAK,CAAC,IAAI,CAAA,CAAA;gBAC5B,KAAK,IAAI,GAAE,GAAI,CAAC,EAAE,GAAE,GAAI,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;oBAC/C,MAAM,QAAO,GAAI,SAAS,CAAC,GAAG,CAAA,CAAA;oBAC9B,IAAI,QAAO,GAAI,GAAE,GAAI,SAAQ,GAAI,CAAA,CAAA;oBACjC,IAAI,OAAM,GAAI,UAAS,GAAI,IAAG,GAAI,CAAA,CAAA;oBAClC,IAAI,SAAQ,GAAI,SAAQ,GAAI,CAAA,CAAA;oBAC5B,IAAI,UAAS,GAAI,UAAS,GAAI,CAAA,CAAA;oBAE9B,OAAM;oBACN,IAAI,IAAG,GAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,IAAC,CAAA;oBAClC,IAAI,SAAQ,GAAI,GAAE,GAAI,SAAQ,GAAI,CAAC,SAAQ,GAAI,CAAC,CAAA,CAAA;oBAChD,IAAI,QAAO,GAAI,UAAS,GAAI,IAAG,GAAI,EAAC,CAAA;oBAEpC,GAAG,CAAC,IAAG,GAAI,IAAG,CAAA;oBAEd,SAAQ;oBACR,IAAI,QAAQ,CAAC,QAAQ,EAAE;wBACrB,GAAG,CAAC,SAAQ,GAAI,MAAK,CAAA;qBACvB;yBAAO;wBACL,QAAO;wBACP,IAAI,QAAQ,CAAC,QAAQ,EAAE;4BACrB,GAAG,CAAC,SAAQ,GAAI,KAAI,CAAA;yBACtB,CAAE,UAAS;6BACN,IAAI,IAAG,IAAK,QAAQ,CAAC,QAAQ,EAAE;4BAClC,GAAG,CAAC,SAAQ,GAAI,MAAM,CAAA;yBACxB;wBACA,OAAM;6BACD;4BACH,GAAG,CAAC,SAAQ,GAAI,MAAM,CAAA;yBACxB;wBAEA,YAAW;wBACX,oBAAmB;wBACnB,eAAc;wBACd,MAAM,MAAM,EAAE,UAAS,GAAI;4BACzB,CAAC,EAAE,QAAQ;4BACX,CAAC,EAAE,OAAO;4BACV,KAAK,EAAE,SAAS;4BAChB,MAAM,EAAE,UAAU;4BAClB,IAAI,EAAE,QAAO;yBACf,CAAA;wBAEA,4BAA2B;wBAC3B,IAAI,OAAM,GAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAA,IAAK,KAAK,CAAC,UAAU,CAAA,CAAA;wBACvD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA,CAAA;wBACnB,IAAG;qBACL;oBAEA,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAA,CAAA;oBAEtC,IAAG,GAAI,QAAQ,CAAC,KAAI,CAAA;oBACpB,IAAI,UAAS,GAAI,GAAE,GAAI,SAAQ,GAAI,CAAC,SAAQ,GAAI,CAAC,CAAA,CAAA;oBACjD,IAAI,SAAQ,GAAI,UAAS,GAAI,IAAG,GAAI,EAAC,CAAA;oBACrC,GAAG,CAAC,IAAG,GAAI,IAAG,CAAA;oBACd,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,CAAA,CAAA;iBAC1C;aACF;YAEA,GAAG,CAAC,MAAM,EAAC,CAAA;YACX,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,EAAC,GAAI,UAAU,EAAC,sCAAA,CAAA,CAAA;QAEnD,CAAA;KACF;CACF,CAAA,CAAA;;;;;WA7RE,GAAA,CAYO,MAAA,EAAA,GAAA,CAAA,EAZD,KAAK,EAAC,MAAM,EAAA,CAAA,EAAA;QAChB,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA,EAFD,KAAK,EAAC,MAAM,EAAA,CAAA,EAAA;YAChB,GAAA,CAAkD,MAAA,EAAA,GAAA,CAAA,EAA5C,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,aAAa,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;QAE1C,GAAA,CAAuD,MAAA,EAAA,GAAA,CAAA;YAAjD,GAAG,EAAC,aAAa;YAAC,KAAK,EAAC,iBAAiB;;QAC/C,GAAA,CAAyE,MAAA,EAAA,GAAA,CAAA;YAAnE,GAAG,EAAC,YAAY;YAAC,KAAK,EAAC,eAAe;YAAE,YAAU,EAAE,IAAA,CAAA,MAAM;;QAChE,GAAA,CAIO,MAAA,EAAA,GAAA,CAAA,EAJD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;YACrB,GAAA,CAAiD,QAAA,EAAA,GAAA,CAAA;gBAAzC,IAAI,EAAC,MAAM;gBAAE,OAAK,EAAE,IAAA,CAAA,OAAO;gBAAE,KAAG,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;YACxC,GAAA,CAAoD,QAAA,EAAA,GAAA,CAAA;gBAA5C,IAAI,EAAC,MAAM;gBAAE,OAAK,EAAE,IAAA,CAAA,SAAS;gBAAE,MAAI,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;YAC3C,GAAA,CAAkD,QAAA,EAAA,GAAA,CAAA;gBAA1C,IAAI,EAAC,MAAM;gBAAE,OAAK,EAAE,IAAA,CAAA,QAAQ;gBAAE,KAAG,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;;QAE3C,GAAA,CAAsD,MAAA,EAAA,IAAA,EAAA,GAAA,CAA7C,IAAA,CAAA,QAAQ,CAAC,QAAQ,CAAA,GAAG,GAAC,GAAA,GAAA,CAAG,IAAA,CAAA,WAAW,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA", "file": "pages/calendar/calendar.uvue", "sourcesContent": ["<template>\n  <view class=\"root\">\n    <view class=\"date\">\n      <text class=\"date-text\">{{ current_month }}</text>\n    </view>\n    <view ref=\"draw-header\" class=\"calendar-header\"></view>\n    <view ref=\"draw-weeks\" class=\"calendar-week\" @touchstart=\"select\"></view>\n    <view class=\"btn-group\">\n      <button size=\"mini\" @click=\"preDate\">上个月</button>\n      <button size=\"mini\" @click=\"gotoToday\">回到今天</button>\n      <button size=\"mini\" @click=\"nextDate\">下个月</button>\n    </view>\n    <view>{{ timeData.fullDate }} {{ current_day }}</view>\n  </view>\n</template>\n<script>\nimport { Calendar, DateType } from './index.uts'\n\ntype CoordsType = {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n  data: DateType\n}\n\nexport default {\n  data () {\n    return {\n      weeks: [] as Array<Array<DateType>>,\n      $coords: [] as Array<CoordsType>,\n      $calendar: new Calendar() as Calendar,\n      timeData: {\n        fullDate: '',\n        year: 0,\n        month: 0,\n        date: 0,\n        day: 0,\n        lunar: '',\n        disabled: false,\n        is_today: false\n      } as DateType,\n      testWidth: 0\n    }\n  },\n  computed: {\n    // 获取月份\n    current_month (): string {\n      const nowDate = this.timeData\n      const month = nowDate.month\n      return month < 10 ? '0' + month : month.toString()\n    },\n    current_day (): string {\n      const time = this.timeData.data\n      if (time == null) {\n        return ''\n      }\n      return time.IMonthCn + time.IDayCn\n    }\n  },\n  created () { },\n  onReady () {\n    const calendar = this.$data['$calendar'] as Calendar\n    this.weeks = calendar.getWeeks()\n    this.timeData = calendar.getDateInfo()\n    // 绘制日历头部\n    this.drawHeader()\n    this.drawWeek(this.weeks, '')\n\n    // 仅自动化测试\n    const header = this.$refs['draw-header'] as UniElement\n    this.testWidth = header.getBoundingClientRect().width;\n  },\n  methods: {\n\n    // 触发整个日历的点击事件，需要计算点击位置\n    select (event: TouchEvent) {\n      const refs = this.$refs['draw-weeks'] as UniElement\n      const rect = refs.getBoundingClientRect();\n      const dom_x = rect.left; // 元素左上角相对于视口的 X 坐标\n      const dom_y = rect.top; // 元素左上角相对于视口的 Y 坐标\n      const touch = event.touches[0];\n      const clientX = touch.clientX; // X 坐标\n      const clientY = touch.clientY; // Y 坐标\n      // 计算点击的相对位置\n      const x = clientX - dom_x\n      const y = clientY - dom_y\n\n      this.clickGrid(x, y)\n    },\n\n    // 点击具体的日历格子\n    clickGrid (x: number, y: number) {\n      // 小格子数组\n      // const gridArray = this.$data.$coords\n      const calendar = this.$data['$calendar'] as Calendar\n      const gridArray = this.$data['$coords'] as Array<CoordsType>\n\n      // 遍历小格子数组，找到最接近点击坐标的小格子\n      for (let i = 0; i < gridArray.length; i++) {\n        const grid = gridArray[i]\n        // 计算小格子理论上的最大值\n        const max_x = grid.x + grid.width\n        const max_y = grid.y + grid.height\n\n        const is_x_limit = grid.x < x && x < max_x\n        const is_y_limit = grid.y < y && y < max_y\n\n        const is_select = is_x_limit && is_y_limit\n\n        if (is_select) {\n          const data = grid.data\n          this.timeData = calendar.getDateInfo(data.fullDate)\n          this.drawWeek(this.weeks, grid.data.fullDate)\n        }\n      }\n    },\n    // 切换上个月\n    preDate () {\n      const fulldate = this.timeData.fullDate\n      const calendar = this.$data['$calendar'] as Calendar\n      let time = calendar.getDate(fulldate, -1, 'month')\n      const newDate = time.year + '-' + time.month + '-1';\n      time = calendar.getDate(newDate)\n\n      this.timeData = calendar.getDateInfo(time.fullDate)\n      this.weeks = calendar.getWeeks(time.fullDate)\n\n      // 判断是否回到当前月份\n      if (this.isCurrentMonth(time.year, time.month)) {\n        this.gotoToday();\n      } else {\n        // 否则正常绘制日历\n        this.drawWeek(this.weeks, time.fullDate)\n      }\n    },\n    // 切换下个他\n    nextDate () {\n      const fulldate = this.timeData.fullDate\n      const calendar = this.$data['$calendar'] as Calendar\n      let time = calendar.getDate(fulldate, 1, 'month')\n\n      const newDate = time.year + '-' + time.month + '-1';\n      time = calendar.getDate(newDate)\n\n      this.timeData = calendar.getDateInfo(time.fullDate)\n      this.weeks = calendar.getWeeks(time.fullDate)\n\n      // 判断是否回到当前月份\n      if (this.isCurrentMonth(time.year, time.month)) {\n        this.gotoToday();\n      } else {\n        // 否则正常绘制日历\n        this.drawWeek(this.weeks, time.fullDate)\n      }\n    },\n    // 回到今天\n    gotoToday () {\n      const calendar = this.$data['$calendar'] as Calendar\n      const time = calendar.getDate()\n      this.timeData = calendar.getDateInfo(time.fullDate)\n      this.weeks = calendar.getWeeks(time.fullDate)\n\n      // 重新绘制日历\n      this.drawWeek(this.weeks, time.fullDate)\n    },\n    // 判断是否为当前月份\n    isCurrentMonth(year: number, month: number): boolean {\n      const today = new Date();\n      return year === today.getFullYear() && month === today.getMonth() + 1;\n    },\n\n    // 绘制日历顶部信息\n    drawHeader () {\n      const refs = this.$refs['draw-header'] as UniElement\n      let ctx = refs.getDrawableContext()\n      if (ctx == null) return\n      const date_header_map = ['一', '二', '三', '四', '五', '六', '日']\n\n      const width = refs.getBoundingClientRect().width\n      const num = date_header_map.length\n      const one_width = width / num\n\n      ctx.font = '12'\n      ctx.textAlign = 'center'\n\n      for (let i = 0; i < num; i++) {\n        let box_left = i * one_width + 2\n        let box_width = one_width - 4\n        let box_height = 26\n\n        // 文本赋值\n        const text = date_header_map[i]\n        let text_left = box_width / 2 + box_left\n        let text_top = box_height / 2 + 6\n\n        ctx.fillText(text, text_left, text_top)\n      }\n      ctx.update()\n    },\n\n    // 绘制日历主体\n    drawWeek (weeks: Array<Array<DateType>>, time: string) {\n      const start_time = Date.now()\n      const refs = this.$refs['draw-weeks'] as UniElement\n      let ctx = refs.getDrawableContext()\n      if (ctx == null) return\n      const dom = refs.getBoundingClientRect()\n      const width = dom.width\n      const height = dom.height\n      let week_len = weeks.length\n      const one_width = width / weeks[0].length\n      const one_height = height / week_len\n\n      if (time !== '') {\n        this.$data['$coords'] = [] as Array<CoordsType>\n        ctx.reset()\n      }\n\n      ctx.textAlign = 'center'\n\n      for (let week = 0; week < week_len; week++) {\n\n        const week_item = weeks[week]\n        for (let day = 0; day < week_item.length; day++) {\n          const day_item = week_item[day]\n          let day_left = day * one_width + 2\n          let day_top = one_height * week + 2\n          let day_width = one_width - 4\n          let day_height = one_height - 4\n\n          // 文本赋值\n          let text = day_item.date.toString()\n          let text_left = day * one_width + (one_width / 2)\n          let text_top = one_height * week + 25\n\n          ctx.font = '16'\n\n          // 日期是否禁用\n          if (day_item.disabled) {\n            ctx.fillStyle = '#ccc'\n          } else {\n            // 是否为今天\n            if (day_item.is_today) {\n              ctx.fillStyle = 'red'\n            } // 是否为选中日期\n            else if (time == day_item.fullDate) {\n              ctx.fillStyle = 'blue';\n            }\n            // 默认颜色\n            else {\n              ctx.fillStyle = '#666';\n            }\n\n            // 第一次渲染获取数据\n            // if (time == '') {\n            // 存储坐标组，用于点击事件\n            const coords: CoordsType = {\n              x: day_left,\n              y: day_top,\n              width: day_width,\n              height: day_height,\n              data: day_item\n            }\n\n            // TODO 兼容安卓data内$开头的属性的赋值问题\n            let gridArr = this.$data['$coords'] as Array<CoordsType>\n            gridArr.push(coords)\n            // }\n          }\n\n          ctx.fillText(text, text_left, text_top)\n\n          text = day_item.lunar\n          let lunar_left = day * one_width + (one_width / 2)\n          let lunar_top = one_height * week + 42\n          ctx.font = '10'\n          ctx.fillText(text, lunar_left, lunar_top)\n        }\n      }\n\n      ctx.update()\n      console.log('diff time', Date.now() - start_time);\n\n    }\n  }\n}\n</script>\n\n<style>\n.root {\n  flex: 1;\n  position: relative;\n  padding: 15px;\n  background-color: #fff;\n}\n\n.calendar-header {\n  height: 30px;\n  margin-bottom: 10px;\n}\n\n.date {\n  margin-bottom: 10px;\n  margin-left: 10px;\n}\n\n.date-text {\n  font-size: 34px;\n  font-weight: bold;\n}\n\n.calendar-week {\n  height: 350px;\n  margin: 2px 0;\n}\n\n.btn-group {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  margin: 20px 0;\n}\n</style>\n"]}