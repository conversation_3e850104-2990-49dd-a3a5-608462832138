<template>
	<form-container :label="fieldName" :show-error="showError" :tip="tip" :error-message="errorMessage" :label-color="labelColor"
		:background-color="backgroundColor">
		<template #input-content>
			<view class="yearmonth-display-container" @click="openYearMonthPicker">
				<view class="yearmonth-icon">
					<text class="yearmonth-icon-text">📅</text>
				</view>
				<text class="yearmonth-text">{{ displayText }}</text>
			</view>
		</template>
	</form-container>

	<!-- 年月选择器 -->
	<main-yearmonth-picker ref="yearmonthPicker" @confirm="onYearMonthConfirm" @cancel="onYearMonthCancel"></main-yearmonth-picker>
</template>

<script lang="uts">
	import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'
	import FormContainer from './form-container.uvue'
	import MainYearmonthPicker from './../tools/main-yearmonth-picker.uvue'

	// 年月数据类型
	type YearMonthData = {
		year: number,
		month: number
	}

	export default {
		name: "FormYearmonth",
		emits: ['change'],
		components: {
			FormContainer,
			MainYearmonthPicker
		},
		props: {
			data: {
				type: null as any as PropType<FormFieldData>
			},
			index: {
				type: Number,
				default: 0
			},
			keyName: {
				type: String,
				default: ""
			},
			labelColor: {
				type: String,
				default: "#000"
			},
			backgroundColor: {
				type: String,
				default: "#f1f4f9"
			}
		},
		data() {
			return {
				fieldName: "",
				fieldValue: "" as string,
				isSave: false,
				save_key: "",
				tip: "",
				displayText: "请选择年月",
				showError: false,
				errorMessage: ""
			}
		},
		computed: {

		},
		watch: {
			data: {
				handler(obj: FormFieldData) {
					// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
					// 这避免了用户输入时的循环更新问题
					const newValue = obj.value as string
					if (newValue !== this.fieldValue) {
						this.fieldValue = newValue
						this.updateDisplayText()
					}
				},
				deep: true
			}
		},
		created(): void {
			// 初始化时调用一次即可
			const fieldObj = this.$props["data"] as FormFieldData
			this.initFieldData(fieldObj)
		},
		methods: {
			// 初始化字段数据（仅在首次加载时调用）
			initFieldData(fieldObj: FormFieldData): void {
				const fieldKey = fieldObj.key
				const fieldValue = fieldObj.value as string

				// 设置基本信息
				this.fieldName = fieldObj.name
				this.fieldValue = fieldValue
				this.isSave = fieldObj.isSave ?? false
				this.save_key = this.keyName + "_" + fieldKey

				// 解析配置信息
				const extalJson = fieldObj.extra as UTSJSONObject
				this.tip = extalJson.getString("tip") ?? ""

				// 更新显示文本
				this.updateDisplayText()

				// 获取缓存
				this.getCache()
			},

			// 更新显示文本
			updateDisplayText(): void {
				if (this.fieldValue != "" ) {
					// 验证格式是否为 YYYY-MM
					const yearMonthPattern = /^\d{4}-\d{2}$/
					if (yearMonthPattern.test(this.fieldValue)) {
						const parts = this.fieldValue.split("-")
						const year = parts[0]
						const month = parts[1]
						this.displayText = `${year}年${month}月`
					} else {
						this.displayText = this.fieldValue
					}
				} else {
					this.displayText = "请选择年月"
				}
			},

			getCache(): void {
				if (this.isSave) {
					const that = this
					uni.getStorage({
						key: this.save_key,
						success: (res: GetStorageSuccess) => {
							const save_value = res.data
							if(typeof save_value === 'string'){
								that.fieldValue = save_value as string
								that.updateDisplayText()
								const result: FormChangeEvent = {
									index: this.index,
									value: save_value
								}
								this.change(result)
							}

						}
					})
				}
			},

			setCache(): void {
				if (this.isSave && typeof this.fieldValue ==="string") {
					uni.setStorage({
						key: this.save_key,
						data: this.fieldValue
					})
				}
			},

			validate(): boolean {
				// 年月值验证
				if (this.fieldValue == "") {
					this.showError = true
					this.errorMessage = "请选择年月"
					return false
				}

				// 验证年月格式 YYYY-MM
				const yearMonthPattern = /^\d{4}-\d{2}$/
				if (!yearMonthPattern.test(this.fieldValue)) {
					this.showError = true
					this.errorMessage = "年月格式不正确"
					return false
				}

				// 验证月份范围
				const parts = this.fieldValue.split("-")
				const month = parseInt(parts[1])
				if (month < 1 || month > 12) {
					this.showError = true
					this.errorMessage = "月份必须在1-12之间"
					return false
				}

				this.showError = false
				this.errorMessage = ""
				return true
			},

			change(event: FormChangeEvent): void {
				// 更新字段值
				this.fieldValue = event.value as string
				// 更新显示文本
				this.updateDisplayText()
				// 保存缓存
				this.setCache()
				// 触发父组件事件
				this.$emit('change', event)
			},

			// 打开年月选择器
			openYearMonthPicker(): void {
				const yearmonthPicker = this.$refs["yearmonthPicker"] as ComponentPublicInstance
				yearmonthPicker.$callMethod("open")
			},

			// 年月选择确认
			onYearMonthConfirm(yearMonthData: UTSJSONObject): void {
				console.log(yearMonthData)
				// 格式化为 YYYY-MM 格式
				const year = yearMonthData.getNumber("year")
				const month = yearMonthData.getNumber("month")
				let yearValue :string=new Date().getFullYear().toString()
				let monthValue :string=new Date().getMonth().toString()

				if(year!=null){
					yearValue=year.toString()
				}
				if(month!=null){
					monthValue=month.toString().padStart(2, '0')
				}
				
				const selectedValue = `${yearValue}-${monthValue}`

				const result: FormChangeEvent = {
					index: this.index,
					value: selectedValue
				}
				this.change(result)
			},

			// 年月选择取消
			onYearMonthCancel(): void {
				// 取消选择，不做任何操作
			}
		}
	}
</script>

<style>
	.yearmonth-display-container {
		flex: 1;
		display: flex;
		flex-direction: row;
		align-items: center;
		min-height: 60rpx;
		padding: 10rpx;
		border-radius: 10rpx;
		background-color: rgba(255, 255, 255, 0.8);
	}

	.yearmonth-icon {
		width: 60rpx;
		height: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-right: 20rpx;
	}

	.yearmonth-icon-text {
		font-size: 32rpx;
	}

	.yearmonth-text {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
	}
</style>